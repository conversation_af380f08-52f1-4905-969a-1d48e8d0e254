{"name": "dashboard-fresh", "version": "1.0.0", "description": "Dashboard frontend for e-commerce analytics SaaS - Fresh implementation", "exports": "./main.ts", "tasks": {"check": "deno fmt --check && deno lint && deno check **/*.ts && deno check **/*.tsx", "cli": "echo \"import '\\$fresh/src/dev/cli.ts'\" | deno run --unstable -A -", "manifest": "deno task cli manifest $(pwd)", "start": "deno run -A --watch=static/,routes/ dev.ts", "build": "deno run -A dev.ts build", "preview": "deno run -A main.ts", "update": "deno run -A -r https://fresh.deno.dev/update .", "dev": "deno run -A --watch=static/,routes/ dev.ts", "test": "deno test -A", "test:unit": "deno test -A tests/unit/", "test:integration": "deno test -A tests/integration/", "test:e2e": "deno test -A tests/e2e/", "test:performance": "k6 run tests/load/dashboard_load_test.js", "test:coverage": "deno test -A --coverage=coverage", "test:watch": "deno test -A --watch", "lint": "deno lint", "fmt": "deno fmt", "fmt:check": "deno fmt --check"}, "lint": {"rules": {"tags": ["fresh", "recommended"]}}, "exclude": ["**/_fresh/*"], "lock": false, "imports": {"$fresh/": "https://deno.land/x/fresh@1.6.1/", "preact": "https://esm.sh/preact@10.19.2", "preact/": "https://esm.sh/preact@10.19.2/", "@preact/signals": "https://esm.sh/*@preact/signals@1.2.1", "@preact/signals-core": "https://esm.sh/*@preact/signals-core@1.5.0", "tailwindcss": "npm:tailwindcss@3.3.0", "tailwindcss/": "npm:/tailwindcss@3.3.0/", "tailwindcss/plugin": "npm:/tailwindcss@3.3.0/plugin.js", "$std/": "https://deno.land/std@0.208.0/", "d3": "https://esm.sh/d3@7.8.5", "@types/d3": "https://esm.sh/@types/d3@7.4.3", "postgres": "https://deno.land/x/postgres@v0.17.0/mod.ts", "redis": "https://deno.land/x/redis@v0.31.0/mod.ts", "bcrypt": "https://deno.land/x/bcrypt@v0.4.1/mod.ts", "djwt": "https://deno.land/x/djwt@v3.0.1/mod.ts", "zod": "https://deno.land/x/zod@v3.22.4/mod.ts", "ecommerce-analytics-shared-types": "../packages/shared-types/mod.ts"}, "compilerOptions": {"jsx": "react-jsx", "jsxImportSource": "preact", "lib": ["dom", "dom.iterable", "es6"], "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false}, "fmt": {"useTabs": false, "lineWidth": 100, "indentWidth": 2, "semiColons": true, "singleQuote": false, "proseWrap": "preserve", "include": ["src/", "routes/", "islands/", "components/", "utils/", "tests/"], "exclude": ["coverage/", "_fresh/", "static/"]}}